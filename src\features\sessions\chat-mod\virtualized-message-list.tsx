import UserImg from "@/assets/user.png";
import { FormatS3ImgUrl } from "@/utils/common";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useCallback, useEffect, useRef, useState } from "react";

interface Message {
  id: number;
  senderId: number;
  replyToId?: number | null;
  moderatorId?: number | null;
  conversationId: number;
  message: string;
  messageType?: string;
  messageStatus?: string;
  isRead?: boolean;
  metadata?: unknown;
  createdAt: string;
  updatedAt: string;
}

interface VirtualizedMessageListProps {
  messages: Message[];
  customerId: number;
  modelAvatar?: string;
  customerAvatar?: string;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  onLoadMore: () => void;
  className?: string;
}

export default function VirtualizedMessageList({
  messages,
  customerId,
  modelAvatar,
  customerAvatar,
  isLoadingMore,
  hasNextPage,
  onLoadMore,
  className = "",
}: VirtualizedMessageListProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Messages should be in chronological order (oldest first, newest last)
  // The API returns them in chronological order, so we don't need to reverse
  const orderedMessages = messages;

  const virtualizer = useVirtualizer({
    count: orderedMessages.length,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback(() => 80, []), // Estimated height per message
    overscan: 5,
    measureElement: (element) => element?.getBoundingClientRect().height ?? 80,
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      weekday: "long",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Handle scroll to load more messages when scrolling to top
  const handleScroll = useCallback(() => {
    const element = parentRef.current;
    if (!element || isLoadingMore || !hasNextPage) return;

    // Check if scrolled to top (with some threshold)
    if (element.scrollTop <= 100) {
      onLoadMore();
    }
  }, [isLoadingMore, hasNextPage, onLoadMore]);

  // Scroll to bottom on initial load
  useEffect(() => {
    if (parentRef.current && isInitialLoad && messages.length > 0) {
      const element = parentRef.current;
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        element.scrollTop = element.scrollHeight;
        setIsInitialLoad(false);
      });
    }
  }, [messages.length, isInitialLoad]);

  // Auto-scroll to bottom when new messages arrive (but not when loading more)
  useEffect(() => {
    if (parentRef.current && !isInitialLoad && !isLoadingMore) {
      const element = parentRef.current;
      const isNearBottom = element.scrollHeight - element.scrollTop - element.clientHeight < 150;

      if (isNearBottom) {
        requestAnimationFrame(() => {
          element.scrollTop = element.scrollHeight;
        });
      }
    }
  }, [messages.length, isInitialLoad, isLoadingMore]);

  return (
    <div
      ref={parentRef}
      onScroll={handleScroll}
      className={`overflow-auto ${className}`}
      style={{ height: "100%" }}
    >
      {/* Loading indicator at top */}
      {isLoadingMore && (
        <div className="text-center text-gray-500 text-sm py-4">
          Loading more messages...
        </div>
      )}

      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: "100%",
          position: "relative",
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const message = orderedMessages[virtualItem.index];
          const prevMessage = virtualItem.index > 0 ? orderedMessages[virtualItem.index - 1] : null;
          
          const isCurrentUser = message.senderId === customerId;
          const showDate =
            !prevMessage ||
            new Date(message.createdAt).toDateString() !==
              new Date(prevMessage.createdAt).toDateString();

          return (
            <div
              key={virtualItem.key}
              data-index={virtualItem.index}
              ref={virtualizer.measureElement}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                transform: `translateY(${virtualItem.start}px)`,
              }}
              className="px-4 py-2"
            >
              {showDate && (
                <div className="text-xs text-gray-600 bg-sidebar text-center mt-4 w-fit px-4 py-2 rounded-full mx-auto mb-4">
                  {formatDate(message.createdAt)}
                </div>
              )}

              <div
                className={`flex gap-2 w-full max-w-[85%] ${
                  isCurrentUser ? "justify-end ms-auto" : ""
                }`}
              >
                {!isCurrentUser && (
                  <img
                    src={
                      modelAvatar
                        ? FormatS3ImgUrl(modelAvatar as string)
                        : UserImg
                    }
                    alt="user"
                    className="w-[40px] h-[40px] rounded-full object-cover"
                  />
                )}

                <div
                  className={`flex flex-col gap-2 ${
                    isCurrentUser ? "items-end" : ""
                  }`}
                >
                  <div
                    className={`w-fit break-words px-4 py-3 rounded-xl text-sm ${
                      isCurrentUser
                        ? "rounded-se-none bg-sidebar-primary text-white"
                        : "rounded-ss-none bg-sidebar-accent"
                    }`}
                  >
                    {message.message}
                  </div>
                </div>

                {isCurrentUser && (
                  <img
                    src={
                      customerAvatar
                        ? FormatS3ImgUrl(customerAvatar as string)
                        : UserImg
                    }
                    alt="user"
                    className="w-[40px] h-[40px] rounded-full object-cover"
                  />
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty state */}
      {messages.length === 0 && !isLoadingMore && (
        <div className="text-center text-gray-500 text-sm py-8">
          No messages yet. Start the conversation!
        </div>
      )}
    </div>
  );
}
