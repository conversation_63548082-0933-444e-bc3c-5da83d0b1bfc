import { useCallback, useEffect, useState } from "react";
import { useGetConversationMessagesInfinite } from "../api";
import useChatStore from "@/stores/useChatStore";

interface ApiMessageData {
  id: number;
  senderId: number;
  replyToId?: number | null;
  moderatorId?: number | null;
  conversationId: number;
  message: string;
  messageType?: string;
  messageStatus?: string;
  isRead?: boolean;
  metadata?: unknown;
  createdAt: string;
  updatedAt: string;
}

interface UseInfiniteMessagesProps {
  conversationId: string;
  limit?: number;
}

export function useInfiniteMessages({
  conversationId,
  limit = 10,
}: UseInfiniteMessagesProps) {
  const [isInitialized, setIsInitialized] = useState(false);

  const {
    moderatorMessages,
    moderatorMessagesMeta,
    isLoadingMoreMessages,
    "mod-setMessages": modSetMessages,
    "mod-appendOlderMessages": modAppendOlderMessages,
    "mod-setLoadingMore": modSetLoadingMore,
  } = useChatStore();

  const conversationIdNum = parseInt(conversationId, 10);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
  } = useGetConversationMessagesInfinite(conversationId, limit);

  const messages = moderatorMessages[conversationIdNum] || [];
  const meta = moderatorMessagesMeta[conversationIdNum];

  // Transform API messages to the format expected by the store
  const transformMessages = useCallback((apiMessages: ApiMessageData[]) => {
    return apiMessages?.length
      ? apiMessages.map((msg: ApiMessageData) => ({
          id: msg.id,
          senderId: msg.senderId,
          replyToId: msg.replyToId || null,
          moderatorId: msg.moderatorId || null,
          conversationId: msg.conversationId,
          message: msg.message,
          messageType: (msg.messageType as "text" | "image" | "file") || "text",
          messageStatus:
            (msg.messageStatus as "sent" | "delivered" | "read") || "sent",
          isRead: msg.isRead || false,
          metadata: msg.metadata,
          createdAt: msg.createdAt,
          updatedAt: msg.updatedAt,
        }))
      : [];
  }, []);

  // Initialize messages on first load
  useEffect(() => {
    if (data?.pages && data.pages.length > 0 && !isInitialized) {
      const firstPage = data.pages[0];
      // Handle the nested structure: data.messages.messages and data.messages.meta
      const messagesData = firstPage?.messages?.messages || firstPage?.messages || [];
      const metaData = firstPage?.messages?.meta || firstPage?.meta || {};

      if (messagesData) {
        const transformedMessages = transformMessages(messagesData);
        console.log("Initial messages loaded:", transformedMessages.length, "messages");
        console.log("First message:", transformedMessages[0]);
        console.log("Last message:", transformedMessages[transformedMessages.length - 1]);
        modSetMessages(conversationIdNum, transformedMessages, metaData);
        setIsInitialized(true);
      }
    }
  }, [
    data?.pages,
    conversationIdNum,
    modSetMessages,
    transformMessages,
    isInitialized,
  ]);

  // Handle loading more messages (older messages)
  const loadMoreMessages = useCallback(async () => {
    if (isFetchingNextPage || !hasNextPage || isLoadingMoreMessages) {
      return;
    }

    modSetLoadingMore(true);

    try {
      const result = await fetchNextPage();

      if (result.data?.pages) {
        const latestPage = result.data.pages[result.data.pages.length - 1];
        // Handle the nested structure: data.messages.messages and data.messages.meta
        const messagesData = latestPage?.messages?.messages || latestPage?.messages || [];
        const metaData = latestPage?.messages?.meta || latestPage?.meta || {};

        if (messagesData && messagesData.length > 0) {
          const transformedMessages = transformMessages(messagesData);
          modAppendOlderMessages(
            conversationIdNum,
            transformedMessages,
            metaData
          );
        }
      }
    } catch (err) {
      console.error("Error loading more messages:", err);
      modSetLoadingMore(false);
    }
  }, [
    isFetchingNextPage,
    hasNextPage,
    isLoadingMoreMessages,
    fetchNextPage,
    transformMessages,
    conversationIdNum,
    modAppendOlderMessages,
    modSetLoadingMore,
  ]);

  // Update loading state
  useEffect(() => {
    if (isFetchingNextPage !== isLoadingMoreMessages) {
      modSetLoadingMore(isFetchingNextPage);
    }
  }, [isFetchingNextPage, isLoadingMoreMessages, modSetLoadingMore]);

  return {
    messages,
    meta,
    isLoading,
    isLoadingMore: isLoadingMoreMessages || isFetchingNextPage,
    hasNextPage,
    isError,
    error,
    loadMoreMessages,
    refetch: () => {
      setIsInitialized(false);
      // The query will automatically refetch and reinitialize
    },
  };
}
